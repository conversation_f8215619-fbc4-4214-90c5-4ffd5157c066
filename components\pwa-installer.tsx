"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useLanguage } from "@/components/language-provider";
import { getTranslations } from "@/lib/i18n/translations";
import { trackPWAInstall, trackPWALaunch } from "@/components/google-analytics";

interface PWAInstallerProps {
  variant?: "default" | "compact";
  className?: string;
}

export function PWAInstaller({ variant = "default", className }: PWAInstallerProps) {
  const [installPrompt, setInstallPrompt] = useState<any>(null);
  const [isInstalled, setIsInstalled] = useState(false);
  const { language } = useLanguage();
  const t = getTranslations(language);

  // Register service worker
  useEffect(() => {
    if (typeof window !== "undefined" && "serviceWorker" in navigator) {
      window.addEventListener("load", () => {
        navigator.serviceWorker
          .register("/sw.js")
          .then((registration) => {
            console.log("Service Worker registered with scope:", registration.scope);
          })
          .catch((error) => {
            console.error("Service Worker registration failed:", error);
          });
      });
    }
  }, []);

  // Function to track installation
  const trackInstallation = async () => {
    console.log('[PWA Installer] Tracking installation...');
    try {
      const response = await fetch('/api/install-counter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          source: 'pwa-installer'
        })
      });
      const result = await response.json();
      console.log('[PWA Installer] Installation tracked successfully:', result);
    } catch (error) {
      console.error('[PWA Installer] Failed to track installation:', error);
    }
  };

  // Handle install prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setInstallPrompt(e);
    };

    // Check if app is already installed and track launch mode
    const checkIsInstalled = () => {
      const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      const isInstalled = isStandalone || isInWebAppiOS;

      if (isInstalled) {
        setIsInstalled(true);
        // Track PWA launch
        trackPWALaunch(isStandalone ? 'standalone' : 'ios-webapp');
      } else {
        // Track browser launch
        trackPWALaunch('browser');
      }
    };

    // Handle app installation
    const handleAppInstalled = (e: Event) => {
      console.log('[PWA Installer] App installed event triggered:', e);
      setIsInstalled(true);
      trackInstallation(); // Track the installation counter
      trackPWAInstall(); // Track in Google Analytics

      // Notify service worker that PWA is installed
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then((registration) => {
          if (registration.active) {
            console.log(`[PWA Installer] SW active. Sending PWA_INSTALLED_WITH_LANG for lang: '${language}'`);
            registration.active.postMessage({
              type: 'PWA_INSTALLED_WITH_LANG',
              lang: language
            });
          } else {
            console.warn('[PWA Installer] SW not active when trying to send PWA_INSTALLED_WITH_LANG.');
          }
        });
      }
    };

    console.log('[PWA Installer] Setting up event listeners...');
    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt as any);
    window.addEventListener("appinstalled", handleAppInstalled);

    checkIsInstalled();

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt as any);
      window.removeEventListener("appinstalled", handleAppInstalled);
    };
  }, []);

  const handleInstallClick = () => {
    console.log('[PWA Installer] Install button clicked');
    if (!installPrompt) {
      console.log('[PWA Installer] No install prompt available');
      return;
    }

    console.log('[PWA Installer] Showing install prompt...');
    // Show the install prompt
    installPrompt.prompt();

    // Wait for the user to respond to the prompt
    installPrompt.userChoice.then((choiceResult: { outcome: string }) => {
      console.log('[PWA Installer] User choice result:', choiceResult);
      if (choiceResult.outcome === "accepted") {
        console.log("[PWA Installer] User accepted the install prompt");
        setIsInstalled(true);
        // Note: trackInstallation will be called by the appinstalled event
      } else {
        console.log("[PWA Installer] User dismissed the install prompt");
      }
      // Clear the saved prompt since it can't be used again
      setInstallPrompt(null);
    });
  };

  if (isInstalled || !installPrompt) {
    return null;
  }

  if (variant === "compact") {
    return (
      <Button
        onClick={handleInstallClick}
        className={`flex items-center gap-1 ${className || ""}`}
        variant="ghost"
        size="sm"
        title={t.installApp || "Install App"}
      >
        <Download className="h-4 w-4" />
        <span className="hidden sm:inline">{t.installApp || "Install App"}</span>
      </Button>
    );
  }

  return (
    <Button
      onClick={handleInstallClick}
      className={`flex items-center gap-2 ${className || ""}`}
      variant="outline"
    >
      <Download className="h-4 w-4" />
      <span>{t.installApp || "Install App"}</span>
    </Button>
  );
}

// Component to handle PWA updates
export function PWAUpdater() {
  useEffect(() => {
    if (typeof window !== "undefined" && "serviceWorker" in navigator) {
      let refreshing = false;

      // When the SW updates, reload the page
      navigator.serviceWorker.addEventListener("controllerchange", () => {
        if (refreshing) return;
        refreshing = true;
        window.location.reload();
      });

      // Check for updates every 60 minutes
      const interval = setInterval(() => {
        navigator.serviceWorker.getRegistration().then((registration) => {
          if (registration) registration.update();
        });
      }, 60 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, []);

  return null;
}
